@extends('layouts.contentNavbarLayout')

@section('title', 'Data Pendapatan')

@section('page-style')
    <style>
        .modern-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 2rem;
            color: white;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .stat-card {
            border: none;
            border-radius: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            position: relative;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-right: 1rem;
        }

        .filter-card {
            border: none;
            border-radius: 20px;
            background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .data-table-card {
            border: none;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .table-modern {
            margin-bottom: 0;
        }

        .table-modern thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.5px;
            padding: 1.2rem 1rem;
            position: relative;
        }

        .table-modern tbody tr {
            transition: all 0.3s ease;
            border-bottom: 1px solid #f1f3f4;
        }

        .table-modern tbody tr:hover {
            background: linear-gradient(90deg, #f8f9ff 0%, #fff 100%);
            transform: scale(1.01);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .table-modern tbody td {
            padding: 1rem;
            vertical-align: middle;
        }

        .badge-modern {
            padding: 0.6rem 1.2rem;
            border-radius: 25px;
            font-weight: 500;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-action {
            border-radius: 12px;
            padding: 0.6rem 1.2rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .dropdown-modern .dropdown-menu {
            border: none;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            padding: 0.5rem 0;
        }

        .dropdown-modern .dropdown-item {
            padding: 0.7rem 1.5rem;
            transition: all 0.2s ease;
            border-radius: 8px;
            margin: 0 0.5rem;
        }

        .dropdown-modern .dropdown-item:hover {
            background: linear-gradient(90deg, #667eea, #764ba2);
            color: white;
            transform: translateX(5px);
        }

        .form-control,
        .form-select {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus,
        .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .input-group-text {
            border-radius: 12px 0 0 12px;
            border: 2px solid #e9ecef;
            border-right: none;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 0;
        }

        .icon-wrapper {
            width: 70px;
            height: 70px;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin-right: 1.5rem;
        }

        @media (max-width: 768px) {
            .modern-header {
                padding: 1.5rem;
                text-align: center;
            }

            .page-title {
                font-size: 2rem;
            }

            .stat-card {
                margin-bottom: 1rem;
            }
        }
    </style>
@endsection

@section('content')
    <!-- Modern Page Header -->
    <div class="modern-header">
        <div class="row align-items-center">
            <div class="col-lg-8 col-md-7">
                <div class="d-flex align-items-center">
                    <div class="icon-wrapper">
                        <i class="bx bx-money"></i>
                    </div>
                    <div>
                        <h1 class="page-title">Data Pendapatan</h1>
                        <p class="page-subtitle">Kelola dan pantau pendapatan perusahaan secara real-time</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-5 text-md-end mt-3 mt-md-0">
                <div class="d-flex flex-column flex-md-row gap-2 justify-content-md-end">
                    <button class="btn btn-light btn-action">
                        <i class="bx bx-download me-2"></i>Export Excel
                    </button>
                    <button class="btn btn-outline-light btn-action">
                        <i class="bx bx-printer me-2"></i>Print Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 col-12 mb-4">
            <div class="card stat-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon bg-success bg-opacity-10 text-success">
                            <i class="bx bx-money"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="text-muted mb-1 fw-medium">Total Pendapatan</p>
                            <h4 class="mb-0 fw-bold text-dark">Rp {{ number_format($totalRevenue ?? 0, 0, ',', '.') }}</h4>
                            <small class="text-success">
                                <i class="bx bx-trending-up me-1"></i>+12.5% dari bulan lalu
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-12 mb-4">
            <div class="card stat-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon bg-primary bg-opacity-10 text-primary">
                            <i class="bx bx-calendar"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="text-muted mb-1 fw-medium">Pendapatan Bulan Ini</p>
                            <h4 class="mb-0 fw-bold text-dark">Rp {{ number_format($monthlyRevenue ?? 0, 0, ',', '.') }}
                            </h4>
                            <small class="text-primary">
                                <i class="bx bx-calendar-check me-1"></i>{{ date('F Y') }}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-12 mb-4">
            <div class="card stat-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon bg-warning bg-opacity-10 text-warning">
                            <i class="bx bx-time-five"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="text-muted mb-1 fw-medium">Pending Pembayaran</p>
                            <h4 class="mb-0 fw-bold text-dark">Rp {{ number_format($pendingRevenue ?? 0, 0, ',', '.') }}
                            </h4>
                            <small class="text-warning">
                                <i class="bx bx-hourglass me-1"></i>Menunggu pembayaran
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-12 mb-4">
            <div class="card stat-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon bg-info bg-opacity-10 text-info">
                            <i class="bx bx-receipt"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="text-muted mb-1 fw-medium">Total Invoice</p>
                            <h4 class="mb-0 fw-bold text-dark">{{ number_format($totalInvoices ?? 0) }}</h4>
                            <small class="text-info">
                                <i class="bx bx-file me-1"></i>Invoice terdaftar
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('pendapatan') }}">
                        <div class="row g-3 align-items-end">
                            <!-- Search Field -->
                            <div class="col-lg-5 col-md-6">
                                <label for="search" class="form-label">Pencarian</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bx bx-search"></i>
                                    </span>
                                    <input type="text" id="search" name="search" value="{{ $search ?? '' }}"
                                        placeholder="Cari nama pelanggan atau paket..." class="form-control">
                                </div>
                            </div>

                            <!-- Status Filter -->
                            <div class="col-lg-2 col-md-6">
                                <label for="status" class="form-label">Status</label>
                                <select id="status" name="status" class="form-select">
                                    <option value="">Semua</option>
                                    @if (isset($statusOptions))
                                        @foreach ($statusOptions as $statusOption)
                                            <option value="{{ $statusOption->id }}"
                                                {{ ($status ?? '') == $statusOption->id ? 'selected' : '' }}>
                                                {{ $statusOption->nama_status }}
                                            </option>
                                        @endforeach
                                    @endif
                                </select>
                            </div>

                            <!-- Date From -->
                            <div class="col-lg-2 col-md-6">
                                <label for="start_date" class="form-label">Dari</label>
                                <input type="date" id="start_date" name="start_date" value="{{ $startDate ?? '' }}"
                                    class="form-control">
                            </div>

                            <!-- Date To -->
                            <div class="col-lg-2 col-md-6">
                                <label for="end_date" class="form-label">Sampai</label>
                                <input type="date" id="end_date" name="end_date" value="{{ $endDate ?? '' }}"
                                    class="form-control">
                            </div>

                            <!-- Action Buttons -->
                            <div class="col-lg-1 col-md-12">
                                <div class="btn-group w-100" role="group">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bx bx-search"></i>
                                    </button>
                                    <a href="{{ route('pendapatan') }}" class="btn btn-outline-secondary">
                                        <i class="bx bx-refresh"></i>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Filters -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="d-flex flex-wrap gap-1">
                                    <small class="text-muted me-2 align-self-center">Filter Cepat:</small>
                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                        onclick="setDateFilter('today')">
                                        Hari Ini
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                        onclick="setDateFilter('month')">
                                        Bulan Ini
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-success"
                                        onclick="setStatusFilter('paid')">
                                        Lunas
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                        onclick="setStatusFilter('unpaid')">
                                        Belum Bayar
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- Modern Data Table -->
    <div class="row">
        <div class="col-12">
            <div class="card data-table-card">
                <div class="card-header border-0 pb-0">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-info bg-opacity-10 text-info me-3"
                                    style="width: 50px; height: 50px; font-size: 1.2rem;">
                                    <i class="bx bx-table"></i>
                                </div>
                                <div>
                                    <h5 class="card-title mb-1 fw-bold">Daftar Pendapatan</h5>
                                    <p class="text-muted mb-0">Data invoice dan pembayaran pelanggan</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 text-md-end mt-3 mt-md-0">
                            <div class="d-flex flex-column flex-md-row align-items-md-center justify-content-md-end gap-2">
                                <small class="text-muted fw-medium">
                                    <i class="bx bx-info-circle me-1"></i>
                                    Menampilkan {{ $invoices->firstItem() ?? 0 }} - {{ $invoices->lastItem() ?? 0 }} dari
                                    {{ $invoices->total() ?? 0 }} data
                                </small>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="refreshData()">
                                        <i class="bx bx-refresh me-1"></i>Refresh
                                    </button>
                                    <button class="btn btn-sm btn-outline-success" onclick="exportData()">
                                        <i class="bx bx-download me-1"></i>Export
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    @if ($invoices && $invoices->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-modern">
                                <thead>
                                    <tr>
                                        <th><i class="bx bx-hash me-1"></i>No</th>
                                        <th><i class="bx bx-calendar me-1"></i>Tanggal</th>
                                        <th><i class="bx bx-user me-1"></i>Pelanggan</th>
                                        <th><i class="bx bx-package me-1"></i>Paket</th>
                                        <th><i class="bx bx-money me-1"></i>Jumlah</th>
                                        <th><i class="bx bx-time me-1"></i>Jatuh Tempo</th>
                                        <th><i class="bx bx-check-circle me-1"></i>Status</th>
                                        <th><i class="bx bx-cog me-1"></i>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($invoices as $index => $invoice)
                                        <tr>
                                            <td>{{ $invoices->firstItem() + $index }}</td>
                                            <td>{{ \Carbon\Carbon::parse($invoice->created_at)->format('d/m/Y') }}</td>
                                            <td>
                                                <div class="fw-medium">
                                                    {{ $invoice->customer->nama_customer ?? 'N/A' }}</div>
                                                <small class="text-muted">{{ $invoice->customer->alamat ?? '' }}</small>
                                            </td>
                                            <td>{{ $invoice->paket->nama_paket ?? 'N/A' }}</td>
                                            <td class="fw-medium">Rp
                                                {{ number_format($invoice->tagihan, 0, ',', '.') }}</td>
                                            <td>{{ \Carbon\Carbon::parse($invoice->jatuh_tempo)->format('d/m/Y') }}
                                            </td>
                                            <td>
                                                @if ($invoice->status)
                                                    @if ($invoice->status->nama_status == 'Sudah Bayar')
                                                        <span class="badge bg-label-success">
                                                            <i class="bx bx-check-circle me-1"></i>Sudah Bayar
                                                        </span>
                                                    @elseif($invoice->status->nama_status == 'Belum Bayar')
                                                        <span class="badge bg-label-danger">
                                                            <i class="bx bx-x-circle me-1"></i>Belum Bayar
                                                        </span>
                                                    @else
                                                        <span class="badge bg-label-secondary">
                                                            {{ $invoice->status->nama_status }}
                                                        </span>
                                                    @endif
                                                @else
                                                    <span class="badge bg-label-secondary">N/A</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="dropdown">
                                                    <button type="button" class="btn p-0 dropdown-toggle hide-arrow"
                                                        data-bs-toggle="dropdown">
                                                        <i class="bx bx-dots-vertical-rounded"></i>
                                                    </button>
                                                    <div class="dropdown-menu">
                                                        <a class="dropdown-item" href="javascript:void(0);"
                                                            onclick="viewInvoiceDetail({{ $invoice->id }})">
                                                            <i class="bx bx-show me-1"></i> Lihat Detail
                                                        </a>
                                                        @if ($invoice->status && $invoice->status->nama_status == 'Belum Bayar')
                                                            <a class="dropdown-item"
                                                                href="/payment/invoice/{{ $invoice->id }}">
                                                                <i class="bx bx-credit-card me-1"></i> Bayar
                                                            </a>
                                                        @endif
                                                        <a class="dropdown-item" href="javascript:void(0);"
                                                            onclick="printInvoice({{ $invoice->id }})">
                                                            <i class="bx bx-printer me-1"></i> Print
                                                        </a>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        @else
                            <!-- Empty State -->
                            <div class="text-center py-5">
                                <div class="mb-3">
                                    <i class="bx bx-receipt display-4 text-muted"></i>
                                </div>
                                <h5 class="mb-2">Tidak ada data pendapatan</h5>
                                <p class="text-muted">Belum ada data pendapatan yang tersedia untuk ditampilkan.</p>
                            </div>
                    @endif
                </div>

                <!-- Pagination -->
                @if ($invoices && $invoices->hasPages())
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                Menampilkan {{ $invoices->firstItem() }} sampai {{ $invoices->lastItem() }} dari
                                {{ $invoices->total() }} hasil
                            </small>
                            <div>
                                {{ $invoices->appends(request()->query())->links() }}
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>

    </div>

    <!-- Invoice Detail Modal -->
    <div class="modal fade" id="invoiceDetailModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Detail Invoice</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="invoiceDetailContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
    </div>

    <!-- Enhanced JavaScript -->
    <script>
        // Auto refresh data every 30 seconds
        setInterval(function() {
            refreshRevenueData();
        }, 30000);

        function refreshRevenueData() {
            const form = document.querySelector('form');
            const formData = new FormData(form);
            const params = new URLSearchParams(formData);

            fetch(`{{ route('pendapatan') }}?${params.toString()}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.text())
                .then(html => {
                    // Update the page content
                    location.reload();
                })
                .catch(error => console.error('Error refreshing data:', error));
        }

        function refreshData() {
            // Show loading toast
            showToast('Memperbarui data...', 'info');
            refreshRevenueData();
        }

        function exportData() {
            showToast('Mengunduh data...', 'info');
            // Implement export functionality
            window.open('/data/pendapatan/export', '_blank');
        }

        function viewInvoiceDetail(invoiceId) {
            // Show loading state
            document.getElementById('invoiceDetailContent').innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Memuat detail invoice...</p>
                </div>
            `;

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('invoiceDetailModal'));
            modal.show();

            // Fetch invoice details
            fetch(`/invoice/detail/${invoiceId}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('invoiceDetailContent').innerHTML = data.html;
                })
                .catch(error => {
                    document.getElementById('invoiceDetailContent').innerHTML = `
                        <div class="text-center py-4">
                            <i class="bx bx-error text-danger display-4"></i>
                            <h5 class="mt-3 text-danger">Gagal Memuat Data</h5>
                            <p class="text-muted">Terjadi kesalahan saat memuat detail invoice</p>
                            <button class="btn btn-primary" onclick="viewInvoiceDetail(${invoiceId})">
                                <i class="bx bx-refresh me-1"></i>Coba Lagi
                            </button>
                        </div>
                    `;
                });
        }

        function printInvoice(invoiceId) {
            showToast('Menyiapkan dokumen untuk dicetak...', 'info');
            window.open(`/invoice/print/${invoiceId}`, '_blank');
        }

        function sendReminder(invoiceId) {
            if (confirm('Kirim reminder pembayaran ke pelanggan?')) {
                showToast('Mengirim reminder...', 'info');
                // Implement send reminder functionality
                fetch(`/invoice/reminder/${invoiceId}`, {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showToast('Reminder berhasil dikirim!', 'success');
                        } else {
                            showToast('Gagal mengirim reminder', 'error');
                        }
                    })
                    .catch(error => {
                        showToast('Terjadi kesalahan', 'error');
                    });
            }
        }

        // Quick filter functions
        function setDateFilter(period) {
            const today = new Date();
            const startDate = document.getElementById('start_date');
            const endDate = document.getElementById('end_date');

            switch (period) {
                case 'today':
                    startDate.value = today.toISOString().split('T')[0];
                    endDate.value = today.toISOString().split('T')[0];
                    break;
                case 'week':
                    const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
                    const weekEnd = new Date(today.setDate(today.getDate() - today.getDay() + 6));
                    startDate.value = weekStart.toISOString().split('T')[0];
                    endDate.value = weekEnd.toISOString().split('T')[0];
                    break;
                case 'month':
                    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                    const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                    startDate.value = monthStart.toISOString().split('T')[0];
                    endDate.value = monthEnd.toISOString().split('T')[0];
                    break;
            }

            // Auto submit form
            document.querySelector('form').submit();
        }

        function setStatusFilter(status) {
            const statusSelect = document.getElementById('status');

            if (status === 'paid') {
                // Find the "Sudah Bayar" option value
                for (let option of statusSelect.options) {
                    if (option.text.includes('Sudah Bayar')) {
                        statusSelect.value = option.value;
                        break;
                    }
                }
            } else if (status === 'unpaid') {
                // Find the "Belum Bayar" option value
                for (let option of statusSelect.options) {
                    if (option.text.includes('Belum Bayar')) {
                        statusSelect.value = option.value;
                        break;
                    }
                }
            }

            // Auto submit form
            document.querySelector('form').submit();
        }

        // Toast notification function
        function showToast(message, type = 'info') {
            // Create toast element
            const toast = document.createElement('div');
            toast.className =
                `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="bx bx-${type === 'error' ? 'error' : type === 'success' ? 'check' : 'info'}-circle me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;

            // Add to page
            let toastContainer = document.querySelector('.toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                document.body.appendChild(toastContainer);
            }

            toastContainer.appendChild(toast);

            // Show toast
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            // Remove after hide
            toast.addEventListener('hidden.bs.toast', () => {
                toast.remove();
            });
        }

        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
@endsection
